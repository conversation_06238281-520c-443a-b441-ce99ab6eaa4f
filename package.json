{"name": "a0-project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "check:setup": "node scripts/check-setup.js", "seed:users": "node scripts/seed-users.js", "seed:data": "node scripts/generate-comprehensive-seed-data.js", "verify:users": "node scripts/verify-users.js", "verify:data": "node scripts/verify-seed-data.js", "test:parking": "node scripts/test-parking-cancellation.js", "test:parking:frontend": "node scripts/test-frontend-parking.js", "test:parking:complete": "node scripts/test-parking-complete.js", "db:seed": "npm run seed:users", "db:seed:all": "npm run seed:users && npm run seed:data", "db:verify": "npm run verify:users", "db:verify:all": "npm run verify:users && npm run verify:data"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@supabase/supabase-js": "^2.49.8", "date-fns": "^4.1.0", "expo": "^52.0.42", "expo-constants": "~17.0.3", "expo-device": "~7.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-location": "~18.0.10", "expo-notifications": "~0.29.9", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-status-bar": "~2.0.1", "expo-camera": "~16.0.8", "expo-barcode-scanner": "~13.0.1", "expo-image-picker": "~16.0.3", "react": "18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.8.0", "react-native-web": "~0.19.6", "sonner-native": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/react-native": "^12.4.2", "@types/jest": "^29.5.8", "@types/react": "~18.3.12", "dotenv": "^16.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "react-test-renderer": "18.3.1", "typescript": "^5.1.3"}}
# =============================================================================
# PRODUCTION ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# Copy this file to .env.production and fill in your actual values
# DO NOT commit .env.production to version control

# Supabase Configuration (REQUIRED)
EXPO_PUBLIC_SUPABASE_URL=your_production_supabase_url_here
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key_here

# Application Configuration
EXPO_PUBLIC_APP_NAME=Smart Office Assistant
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_ENVIRONMENT=production

# Security Configuration
EXPO_PUBLIC_SESSION_TIMEOUT=3600000
EXPO_PUBLIC_MAX_LOGIN_ATTEMPTS=5
EXPO_PUBLIC_LOCKOUT_DURATION=900000

# Feature Flags
EXPO_PUBLIC_ENABLE_DEBUG_LOGGING=false
EXPO_PUBLIC_ENABLE_ERROR_REPORTING=true
EXPO_PUBLIC_ENABLE_ANALYTICS=true

# API Configuration
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_MAX_RETRY_ATTEMPTS=3

# Notification Configuration
EXPO_PUBLIC_NOTIFICATION_ENABLED=true

# Web Security Configuration
EXPO_PUBLIC_CSP_ENABLED=true

# Environment Variables for User Seed Script
# Copy this file to .env.seed and fill in your actual values
# DO NOT commit .env.seed to version control

# Supabase Configuration (REQUIRED)
# Get these values from your Supabase Dashboard → Settings → API
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Alternative: Use existing environment variables
# If you already have these set in .env, the script will use them
# EXPO_PUBLIC_SUPABASE_URL=your_supabase_url_here

# Instructions:
# 1. Go to your Supabase Dashboard
# 2. Navigate to Settings → API
# 3. Copy the "Project URL" to SUPABASE_URL
# 4. Copy the "service_role" key (NOT the anon key) to SUPABASE_SERVICE_ROLE_KEY
# 5. Save this file as .env.seed (remove .template extension)
# 6. Run: npm run seed:users

# Security Notes:
# - The service_role key has admin privileges - keep it secure
# - Never commit this file to version control
# - Use different keys for development and production
# - Rotate keys regularly for security

// Smart Office Assistant - Enhanced Check-in Method Selector Component
// Advanced component showing multiple verification methods with confidence levels and real-time feedback

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  ActivityIndicator,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { toast } from 'sonner-native';
import { attendanceVerificationService, type VerificationResult } from '../services/AttendanceVerificationService';
import { qrCodeService, type QRScanResult } from '../services/QRCodeService';

const { width } = Dimensions.get('window');

interface VerificationMethod {
  id: string;
  name: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  status: 'available' | 'testing' | 'success' | 'failed' | 'unavailable';
  confidence: number;
  result?: VerificationResult;
  error?: string;
  isPrimary?: boolean;
}

interface Props {
  visible: boolean;
  onClose: () => void;
  onMethodSelected: (method: string, result: VerificationResult) => void;
  demoMode?: boolean;
}

export default function EnhancedCheckInMethodSelector({ 
  visible, 
  onClose, 
  onMethodSelected,
  demoMode = false 
}: Props) {
  const [methods, setMethods] = useState<VerificationMethod[]>([
    {
      id: 'qr_code',
      name: 'QR Code Scan',
      description: 'Scan office QR code for instant verification',
      icon: 'qr-code',
      color: '#4A80F0',
      status: 'available',
      confidence: 0,
      isPrimary: true,
    },
    {
      id: 'gps_location',
      name: 'GPS Location',
      description: 'Verify your location using GPS coordinates',
      icon: 'location',
      color: '#34C759',
      status: 'available',
      confidence: 0,
    },
    {
      id: 'wifi_network',
      name: 'WiFi Network',
      description: 'Detect office WiFi networks',
      icon: 'wifi',
      color: '#FF9500',
      status: 'available',
      confidence: 0,
    },
    {
      id: 'manual_approval',
      name: 'Manual Approval',
      description: 'Request admin approval for check-in',
      icon: 'person-circle',
      color: '#FF2D55',
      status: 'available',
      confidence: 0,
    },
  ]);

  const [isScanning, setIsScanning] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [showConfidenceDetails, setShowConfidenceDetails] = useState(false);

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(300);

  useEffect(() => {
    if (visible) {
      // Start entrance animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto-test methods in demo mode
      if (demoMode) {
        setTimeout(() => testAllMethods(), 1000);
      }
    } else {
      fadeAnim.setValue(0);
      slideAnim.setValue(300);
    }
  }, [visible]);

  const testAllMethods = async () => {
    for (const method of methods) {
      await testMethod(method.id);
      await new Promise(resolve => setTimeout(resolve, 800)); // Delay between tests
    }
  };

  const testMethod = async (methodId: string) => {
    setMethods(prev => prev.map(m => 
      m.id === methodId 
        ? { ...m, status: 'testing', confidence: 0 }
        : m
    ));

    try {
      let result: VerificationResult;

      switch (methodId) {
        case 'qr_code':
          result = await simulateQRVerification();
          break;
        case 'gps_location':
          result = await attendanceVerificationService.verifyGPSLocation(
            37.7749, -122.4194 // Demo coordinates
          );
          break;
        case 'wifi_network':
          result = await attendanceVerificationService.verifyWiFiNetwork('OfficeWiFi');
          break;
        case 'manual_approval':
          result = await simulateManualApproval();
          break;
        default:
          throw new Error('Unknown verification method');
      }

      setMethods(prev => prev.map(m => 
        m.id === methodId 
          ? { 
              ...m, 
              status: result.success ? 'success' : 'failed',
              confidence: result.confidence,
              result,
              error: result.error
            }
          : m
      ));

    } catch (error) {
      setMethods(prev => prev.map(m => 
        m.id === methodId 
          ? { 
              ...m, 
              status: 'failed',
              confidence: 0,
              error: error.message
            }
          : m
      ));
    }
  };

  const simulateQRVerification = async (): Promise<VerificationResult> => {
    // Simulate QR code scanning
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    return {
      success: true,
      method: 'qr_code',
      confidence: 1.0,
      data: {
        code_value: 'SMARTOFFICE_MAIN_ENTRANCE_2024_Q4',
        location_description: 'Main Entrance Lobby',
        qr_code_id: 'demo-qr-1',
        office_location_id: 'demo-office-1',
        scan_count: 42
      }
    };
  };

  const simulateManualApproval = async (): Promise<VerificationResult> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      method: 'manual_approval',
      confidence: 0.8,
      data: {
        approval_required: true,
        estimated_approval_time: '5-10 minutes',
        admin_notified: true
      }
    };
  };

  const handleMethodPress = async (method: VerificationMethod) => {
    if (method.status === 'testing') return;

    setSelectedMethod(method.id);

    if (method.id === 'qr_code') {
      await handleQRCodeScan();
    } else if (method.status === 'success' && method.result) {
      onMethodSelected(method.id, method.result);
      onClose();
    } else {
      await testMethod(method.id);
    }
  };

  const handleQRCodeScan = async () => {
    setIsScanning(true);
    
    try {
      const scanResult: QRScanResult = await qrCodeService.scanQRCodeWithCamera();
      
      if (scanResult.success && scanResult.data) {
        const verificationResult = await attendanceVerificationService.verifyQRCode(scanResult.data);
        
        setMethods(prev => prev.map(m => 
          m.id === 'qr_code' 
            ? { 
                ...m, 
                status: verificationResult.success ? 'success' : 'failed',
                confidence: verificationResult.confidence,
                result: verificationResult,
                error: verificationResult.error
              }
            : m
        ));

        if (verificationResult.success) {
          onMethodSelected('qr_code', verificationResult);
          onClose();
        } else {
          toast.error(verificationResult.error || 'QR code verification failed');
        }
      } else {
        toast.error(scanResult.error || 'Failed to scan QR code');
      }
    } catch (error) {
      console.error('QR scanning error:', error);
      toast.error('QR scanning failed');
    } finally {
      setIsScanning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'testing': return 'hourglass';
      case 'success': return 'checkmark-circle';
      case 'failed': return 'close-circle';
      case 'unavailable': return 'ban';
      default: return 'ellipse';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'testing': return '#FF9500';
      case 'success': return '#34C759';
      case 'failed': return '#FF3B30';
      case 'unavailable': return '#8E8E93';
      default: return '#C7C7CC';
    }
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.9) return 'Excellent';
    if (confidence >= 0.7) return 'Good';
    if (confidence >= 0.5) return 'Fair';
    if (confidence > 0) return 'Poor';
    return 'Not tested';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return '#34C759';
    if (confidence >= 0.7) return '#4A80F0';
    if (confidence >= 0.5) return '#FF9500';
    if (confidence > 0) return '#FF3B30';
    return '#8E8E93';
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View 
        style={[
          styles.overlay,
          { opacity: fadeAnim }
        ]}
      >
        <Animated.View 
          style={[
            styles.container,
            { transform: [{ translateY: slideAnim }] }
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Choose Verification Method</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Demo Mode Indicator */}
          {demoMode && (
            <View style={styles.demoIndicator}>
              <Ionicons name="flask" size={16} color="#FF9500" />
              <Text style={styles.demoText}>Demo Mode - Simulated Results</Text>
            </View>
          )}

          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* Methods List */}
            <View style={styles.methodsList}>
              {methods.map((method) => (
                <TouchableOpacity
                  key={method.id}
                  style={[
                    styles.methodCard,
                    method.isPrimary && styles.primaryMethodCard,
                    selectedMethod === method.id && styles.selectedMethodCard,
                    method.status === 'success' && styles.successMethodCard,
                  ]}
                  onPress={() => handleMethodPress(method)}
                  disabled={method.status === 'testing'}
                >
                  <View style={styles.methodContent}>
                    <View style={[styles.methodIcon, { backgroundColor: method.color }]}>
                      <Ionicons name={method.icon} size={24} color="#FFFFFF" />
                    </View>

                    <View style={styles.methodInfo}>
                      <View style={styles.methodHeader}>
                        <Text style={styles.methodName}>{method.name}</Text>
                        {method.isPrimary && (
                          <View style={styles.primaryBadge}>
                            <Text style={styles.primaryText}>Recommended</Text>
                          </View>
                        )}
                      </View>
                      <Text style={styles.methodDescription}>{method.description}</Text>
                      
                      {/* Confidence Level */}
                      {method.confidence > 0 && (
                        <View style={styles.confidenceContainer}>
                          <Text style={styles.confidenceLabel}>Confidence: </Text>
                          <Text style={[
                            styles.confidenceValue,
                            { color: getConfidenceColor(method.confidence) }
                          ]}>
                            {getConfidenceText(method.confidence)} ({Math.round(method.confidence * 100)}%)
                          </Text>
                        </View>
                      )}

                      {/* Error Message */}
                      {method.error && (
                        <Text style={styles.errorText}>{method.error}</Text>
                      )}
                    </View>

                    <View style={styles.methodStatus}>
                      {method.status === 'testing' ? (
                        <ActivityIndicator size="small" color={method.color} />
                      ) : (
                        <Ionicons 
                          name={getStatusIcon(method.status)} 
                          size={24} 
                          color={getStatusColor(method.status)} 
                        />
                      )}
                    </View>
                  </View>

                  {/* Confidence Bar */}
                  {method.confidence > 0 && (
                    <View style={styles.confidenceBar}>
                      <View 
                        style={[
                          styles.confidenceBarFill,
                          { 
                            width: `${method.confidence * 100}%`,
                            backgroundColor: getConfidenceColor(method.confidence)
                          }
                        ]}
                      />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.retryButton}
                onPress={testAllMethods}
              >
                <Ionicons name="refresh" size={20} color="#4A80F0" />
                <Text style={styles.retryButtonText}>Test All Methods</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.helpButton}
                onPress={() => setShowConfidenceDetails(true)}
              >
                <Ionicons name="help-circle" size={20} color="#666" />
                <Text style={styles.helpButtonText}>How it works</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>

          {/* QR Scanning Overlay */}
          {isScanning && (
            <View style={styles.scanningOverlay}>
              <View style={styles.scanningContent}>
                <ActivityIndicator size="large" color="#4A80F0" />
                <Text style={styles.scanningText}>Scanning QR Code...</Text>
                <TouchableOpacity 
                  style={styles.cancelScanButton}
                  onPress={() => setIsScanning(false)}
                >
                  <Text style={styles.cancelScanText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </Animated.View>
      </Animated.View>

      {/* Confidence Details Modal */}
      <Modal
        visible={showConfidenceDetails}
        transparent={true}
        animationType="fade"
      >
        <View style={styles.detailsOverlay}>
          <View style={styles.detailsContent}>
            <Text style={styles.detailsTitle}>Verification Confidence Levels</Text>
            
            <View style={styles.confidenceLevels}>
              {[
                { level: 'Excellent (90-100%)', color: '#34C759', description: 'Highly reliable verification' },
                { level: 'Good (70-89%)', color: '#4A80F0', description: 'Reliable with minor uncertainties' },
                { level: 'Fair (50-69%)', color: '#FF9500', description: 'Acceptable but may need backup' },
                { level: 'Poor (1-49%)', color: '#FF3B30', description: 'Low confidence, use alternative' },
              ].map((item, index) => (
                <View key={index} style={styles.confidenceLevel}>
                  <View style={[styles.levelIndicator, { backgroundColor: item.color }]} />
                  <View style={styles.levelInfo}>
                    <Text style={styles.levelText}>{item.level}</Text>
                    <Text style={styles.levelDescription}>{item.description}</Text>
                  </View>
                </View>
              ))}
            </View>

            <TouchableOpacity 
              style={styles.closeDetailsButton}
              onPress={() => setShowConfidenceDetails(false)}
            >
              <Text style={styles.closeDetailsText}>Got it</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
    minHeight: '60%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  closeButton: {
    padding: 4,
  },
  demoIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF3E0',
    paddingVertical: 8,
    gap: 6,
  },
  demoText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FF9500',
  },
  scrollView: {
    flex: 1,
  },
  methodsList: {
    padding: 20,
  },
  methodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  primaryMethodCard: {
    borderColor: '#4A80F0',
    backgroundColor: '#F0F4FF',
  },
  selectedMethodCard: {
    borderColor: '#4A80F0',
    borderWidth: 2,
  },
  successMethodCard: {
    borderColor: '#34C759',
    backgroundColor: '#F0FFF4',
  },
  methodContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  methodInfo: {
    flex: 1,
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  primaryBadge: {
    backgroundColor: '#4A80F0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  primaryText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  methodDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  confidenceLabel: {
    fontSize: 12,
    color: '#666',
  },
  confidenceValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 12,
    color: '#FF3B30',
    marginTop: 4,
  },
  methodStatus: {
    marginLeft: 12,
  },
  confidenceBar: {
    height: 4,
    backgroundColor: '#E5E5E7',
    borderRadius: 2,
    marginTop: 12,
    overflow: 'hidden',
  },
  confidenceBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 12,
  },
  retryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0F4FF',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#4A80F0',
    gap: 6,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4A80F0',
  },
  helpButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8F9FA',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E7',
    gap: 6,
  },
  helpButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  scanningOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    minWidth: 200,
  },
  scanningText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1D1D1F',
    marginTop: 16,
    marginBottom: 20,
  },
  cancelScanButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
  },
  cancelScanText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  detailsOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  detailsContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    textAlign: 'center',
    marginBottom: 20,
  },
  confidenceLevels: {
    marginBottom: 24,
  },
  confidenceLevel: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  levelIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  levelInfo: {
    flex: 1,
  },
  levelText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 2,
  },
  levelDescription: {
    fontSize: 12,
    color: '#666',
  },
  closeDetailsButton: {
    backgroundColor: '#4A80F0',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeDetailsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

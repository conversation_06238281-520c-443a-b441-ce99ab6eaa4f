# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# Environment files
.env
.env.local
.env.production
.env.staging
.env*.local

# Production builds
dist/
build/
web-build/

# Development files (exclude from production)
__tests__/
coverage/
*.test.ts
*.test.tsx
test-*.js
debug-*.js
create-*.js
check-*.js
fix-*.js

# Security - Never commit these files
*.key
*.pem
*.p12
*.p8
*.mobileprovision
secrets/
config/secrets.json

# Development documentation
test-app-functionality.md
TESTING_SUMMARY.md
debug*.html
test-app.html

# typescript
*.tsbuildinfo
